package com.cdkit.modules.cm.application.budget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.budget.service.AnnualRevenueRemainingBudgetCalculationService;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetNumberGenerationService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetSaveService;
import com.cdkit.modules.cm.domain.budget.service.QuarterTimeCalculationService;
import com.cdkit.modules.cm.domain.budget.service.RevenueDetailQueryService;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetRealTimeCalculationService;
import com.cdkit.modules.cm.domain.budget.service.RealTimeCalculationRequest;
import com.cdkit.modules.cm.domain.budget.service.RealTimeCalculationResult;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageDetail;
import com.cdkit.modules.cm.domain.budget.service.CenterIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.NonOperatingIndirectCostInfo;
import com.cdkit.modules.cm.domain.budget.service.ComprehensiveIndirectCostInfo;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationRequestDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationResponseDTO;
import com.cdkit.modules.cm.domain.project.mode.entity.CostMaterialDetailEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算应用服务
 * 处理季度预算相关的业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetApplication {

    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;
    private final QuarterlyBudgetSaveService quarterlyBudgetSaveService;
    private final QuarterlyBudgetNumberGenerationService quarterlyBudgetNumberGenerationService;
    private final QuarterTimeCalculationService quarterTimeCalculationService;
    private final ProcurementPackageQueryService procurementPackageQueryService;
    private final CostProjectPlanRepository costProjectPlanRepository;
    private final RevenueDetailQueryService revenueDetailQueryService;
    private final AnnualRevenueRemainingBudgetCalculationService annualRevenueRemainingBudgetCalculationService;
    private final QuarterlyBudgetRealTimeCalculationService quarterlyBudgetRealTimeCalculationService;

    /**
     * 分页查询季度预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostQuarterlyBudgetEntity> queryPageList(CostQuarterlyBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costQuarterlyBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询季度预算详情
     *
     * @param id 季度预算ID
     * @return 季度预算实体
     */
    public CostQuarterlyBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        CostQuarterlyBudgetEntity entity = costQuarterlyBudgetRepository.findById(id);
        if (entity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        log.info("查询季度预算详情成功，ID: {}, 预算单号: {}", id, entity.getQuarterlyBudgetNo());
        return entity;
    }

    /**
     * 新增季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (existingEntity != null) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        // 使用领域服务保存主表和所有子表数据
        String savedId = quarterlyBudgetSaveService.saveMainWithDetails(entity);
        log.info("新增季度预算成功，ID: {}, 预算单号: {}", savedId, entity.getQuarterlyBudgetNo());

        return savedId;
    }

    /**
     * 编辑季度预算
     *
     * @param entity 季度预算实体
     * @return 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String edit(CostQuarterlyBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("季度预算数据不能为空");
        }

        if (!StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + entity.getId());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证季度预算单号唯一性（排除自身）
        if (StringUtils.hasText(entity.getQuarterlyBudgetNo())) {
            CostQuarterlyBudgetEntity duplicateEntity = costQuarterlyBudgetRepository.findByQuarterlyBudgetNo(entity.getQuarterlyBudgetNo());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("季度预算单号已存在：" + entity.getQuarterlyBudgetNo());
            }
        }

        CostQuarterlyBudgetEntity updatedEntity = costQuarterlyBudgetRepository.updateById(entity);
        log.info("编辑季度预算成功，ID: {}, 预算单号: {}", updatedEntity.getId(), updatedEntity.getQuarterlyBudgetNo());
        
        return updatedEntity.getId();
    }

    /**
     * 根据ID删除季度预算
     *
     * @param id 季度预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("季度预算ID不能为空");
        }

        // 验证季度预算是否存在
        CostQuarterlyBudgetEntity existingEntity = costQuarterlyBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("季度预算不存在，ID: " + id);
        }

        costQuarterlyBudgetRepository.deleteById(id);
        log.info("删除季度预算成功，ID: {}, 预算单号: {}", id, existingEntity.getQuarterlyBudgetNo());
    }

    /**
     * 批量删除季度预算
     *
     * @param ids 季度预算ID列表，逗号分隔
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("季度预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));
        
        // 验证所有ID对应的季度预算是否存在
        List<CostQuarterlyBudgetEntity> existingEntities = costQuarterlyBudgetRepository.findByIds(idList);
        if (existingEntities.size() != idList.size()) {
            throw new IllegalArgumentException("部分季度预算不存在，请检查ID列表");
        }

        costQuarterlyBudgetRepository.deleteByIds(idList);
        log.info("批量删除季度预算成功，删除数量: {}, IDs: {}", idList.size(), ids);
    }

    /**
     * 生成下一个季度预算编号
     *
     * @return 下一个季度预算编号（JDYS+8位日期+3位流水）
     */
    public String generateNextQuarterlyBudgetNo() {
        log.info("开始生成下一个季度预算编号");

        String nextBudgetNo = quarterlyBudgetNumberGenerationService.generateNextQuarterlyBudgetNo();

        log.info("生成下一个季度预算编号成功，编号: {}", nextBudgetNo);
        return nextBudgetNo;
    }

    /**
     * 获取季度下拉框选项
     * 返回当年四个季度和下一年第一季度的选项列表（共5个选项）
     *
     * @return 季度选项列表
     */
    public List<String> getQuarterOptions() {
        log.info("开始获取季度下拉框选项");

        List<String> quarterOptions = quarterTimeCalculationService.getQuarterOptions();

        log.info("获取季度下拉框选项成功，共{}个选项", quarterOptions.size());
        return quarterOptions;
    }

    /**
     * 根据选择季度计算开始结束时间
     *
     * @param quarter 季度标识（如"2025年第一季度"）
     * @return 该季度的开始日期和结束日期
     */
    public QuarterTimeCalculationService.QuarterDateRange getQuarterDateRange(String quarter) {
        log.info("开始计算季度日期范围，季度标识: {}", quarter);

        if (!StringUtils.hasText(quarter)) {
            throw new IllegalArgumentException("季度标识不能为空");
        }

        QuarterTimeCalculationService.QuarterDateRange dateRange =
                quarterTimeCalculationService.calculateQuarterDateRange(quarter);

        log.info("计算季度日期范围成功，季度: {}, 开始日期: {}, 结束日期: {}",
                quarter, dateRange.getStartDate(), dateRange.getEndDate());
        return dateRange;
    }

    /**
     * 查询采办包预算科目信息
     * 根据季度计划ID查询原材料及主要原料的预算科目信息
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 采办包预算科目信息列表
     */
    public List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> queryProcurementPackageSubjects(String quarterlyPlanId) {
        log.info("开始查询采办包预算科目信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }

            // 调用领域服务查询
            List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> subjectInfoList =
                procurementPackageQueryService.queryProcurementPackageSubjects(quarterlyPlanId);

            log.info("查询采办包预算科目信息成功，季度计划ID: {}, 查询到{}条记录",
                    quarterlyPlanId, subjectInfoList.size());

            return subjectInfoList;

        } catch (Exception e) {
            log.error("查询采办包预算科目信息失败，季度计划ID: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询采办包预算科目信息失败：" + e.getMessage());
        }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostQuarterlyBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getQuarterlyBudgetName())) {
            throw new IllegalArgumentException("季度预算名称不能为空");
        }

        if (!StringUtils.hasText(entity.getVersion())) {
            throw new IllegalArgumentException("版本不能为空");
        }

        if (!StringUtils.hasText(entity.getBudgetStatus())) {
            throw new IllegalArgumentException("预算状态不能为空");
        }
    }

    /**
     * 根据季度计划ID查询原材料明细
     * 通过季度计划ID查询关联的项目计划的原材料明细数据
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 原材料明细列表
     */
    public List<CostMaterialDetailEntity> queryMaterialDetailByQuarterlyPlanId(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始根据季度计划ID查询原材料明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用项目计划仓储查询原材料明细
            List<CostMaterialDetailEntity> materialDetailList = costProjectPlanRepository.queryMaterialDetailByPlanId(quarterlyPlanId);

            if (materialDetailList == null) {
                materialDetailList = new ArrayList<>();
            }

            log.info("根据季度计划ID查询原材料明细成功，quarterlyPlanId: {}, 查询到 {} 条记录",
                    quarterlyPlanId, materialDetailList.size());

            return materialDetailList;

        } catch (Exception e) {
            log.error("根据季度计划ID查询原材料明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询原材料明细失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据季度计划ID查询收入明细
     * 通过季度计划ID查询关联的项目计划明细数据，转换为收入明细格式返回
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 收入明细列表
     */
    public List<RevenueDetailQueryService.RevenueDetailInfo> queryRevenueDetailByQuarterlyPlanId(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始根据季度计划ID查询收入明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用领域服务查询收入明细数据
            List<RevenueDetailQueryService.RevenueDetailInfo> revenueDetailList =
                revenueDetailQueryService.queryRevenueDetailByQuarterlyPlanId(quarterlyPlanId);

            if (revenueDetailList == null) {
                revenueDetailList = new ArrayList<>();
            }

            log.info("根据季度计划ID查询收入明细成功，quarterlyPlanId: {}, 查询到 {} 条记录",
                    quarterlyPlanId, revenueDetailList.size());

            return revenueDetailList;

        } catch (Exception e) {
            log.error("根据季度计划ID查询收入明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw new RuntimeException("查询收入明细失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据季度计划ID查询年度收入剩余预算金额
     * 计算公式：年度收入剩余预算金额 = 年度预算的收入预算金额 - 与该年度预算相关的已审批通过的季度预算的项目预算总额
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 年度收入剩余预算详细信息
     */
    public AnnualRevenueRemainingBudgetCalculationService.AnnualRevenueRemainingBudgetInfo queryAnnualRevenueRemainingBudget(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始查询年度收入剩余预算金额，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 调用领域服务计算年度收入剩余预算金额
            AnnualRevenueRemainingBudgetCalculationService.AnnualRevenueRemainingBudgetInfo budgetInfo =
                annualRevenueRemainingBudgetCalculationService.calculateAnnualRevenueRemainingBudgetInfo(quarterlyPlanId);

            log.info("查询年度收入剩余预算金额成功，quarterlyPlanId: {}, 剩余预算金额: {}元",
                    quarterlyPlanId, budgetInfo.getAnnualRevenueRemainingBudgetAmount());

            return budgetInfo;

        } catch (Exception e) {
            log.error("查询年度收入剩余预算金额失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            throw e;
        }
    }

    /**
     * 季度预算实时计算
     * 根据输入的采办包变更数据和基础信息，实时计算季度预算的各项金额
     *
     * @param request 实时计算请求参数
     * @return 实时计算结果
     */
    public QuarterlyBudgetRealTimeCalculationResponseDTO realTimeCalculation(QuarterlyBudgetRealTimeCalculationRequestDTO request) {
        log.info("开始执行季度预算实时计算，季度计划ID: {}, 年度预算ID: {}",
                request.getQuarterlyPlanId(), request.getAnnualBudgetId());

        try {
            // 参数校验
            if (!StringUtils.hasText(request.getQuarterlyPlanId())) {
                throw new IllegalArgumentException("季度计划ID不能为空");
            }
            if (!StringUtils.hasText(request.getAnnualBudgetId())) {
                throw new IllegalArgumentException("年度预算ID不能为空");
            }

            // 转换为领域请求对象
            RealTimeCalculationRequest domainRequest = convertToRealTimeCalculationRequest(request);

            // 调用领域服务进行实时计算
            RealTimeCalculationResult domainResult =
                quarterlyBudgetRealTimeCalculationService.calculate(domainRequest);

            // 转换为DTO响应对象
            QuarterlyBudgetRealTimeCalculationResponseDTO response =
                convertToRealTimeCalculationResponseDTO(domainResult);

            log.info("季度预算实时计算完成，季度计划ID: {}, 项目支出预算总额: {}元, 间接费预算总额: {}元",
                    request.getQuarterlyPlanId(),
                    response.getProjectExpenditureBudgetTotal(),
                    response.getIndirectCostBudgetTotal());

            return response;

        } catch (Exception e) {
            log.error("季度预算实时计算失败，季度计划ID: {}, 年度预算ID: {}",
                    request.getQuarterlyPlanId(), request.getAnnualBudgetId(), e);
            throw e;
        }
    }

    /**
     * 转换DTO请求为领域请求对象
     */
    private RealTimeCalculationRequest convertToRealTimeCalculationRequest(QuarterlyBudgetRealTimeCalculationRequestDTO dto) {
        RealTimeCalculationRequest request = new RealTimeCalculationRequest();
        request.setQuarterlyPlanId(dto.getQuarterlyPlanId());
        request.setAnnualBudgetId(dto.getAnnualBudgetId());
        request.setProjectRevenueBudgetTotal(dto.getProjectRevenueBudgetTotal());

        // 转换采办包明细
        if (dto.getProcurementPackageDetails() != null) {
            List<ProcurementPackageDetail> details = dto.getProcurementPackageDetails().stream()
                .map(dtoDetail -> {
                    ProcurementPackageDetail detail = new ProcurementPackageDetail();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setAmount(dtoDetail.getAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setProcurementPackageDetails(details);
        }

        // 转换本中心间接成本明细
        if (dto.getCenterIndirectCostDetails() != null) {
            List<CenterIndirectCostInfo> centerDetails = dto.getCenterIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    CenterIndirectCostInfo detail = new CenterIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setCenterIndirectCostDetails(centerDetails);
        }

        // 转换非经营中心间接成本明细
        if (dto.getNonOperatingIndirectCostDetails() != null) {
            List<NonOperatingIndirectCostInfo> nonOperatingDetails = dto.getNonOperatingIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    NonOperatingIndirectCostInfo detail = new NonOperatingIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setNonOperatingIndirectCostDetails(nonOperatingDetails);
        }

        // 转换综合管理间接成本明细
        if (dto.getComprehensiveIndirectCostDetails() != null) {
            List<ComprehensiveIndirectCostInfo> comprehensiveDetails = dto.getComprehensiveIndirectCostDetails().stream()
                .map(dtoDetail -> {
                    ComprehensiveIndirectCostInfo detail = new ComprehensiveIndirectCostInfo();
                    detail.setBudgetSubjectCode(dtoDetail.getBudgetSubjectCode());
                    detail.setBudgetSubjectName(dtoDetail.getBudgetSubjectName());
                    detail.setExpenditureBudgetAmount(dtoDetail.getExpenditureBudgetAmount());
                    return detail;
                })
                .collect(Collectors.toList());
            request.setComprehensiveIndirectCostDetails(comprehensiveDetails);
        }

        return request;
    }

    /**
     * 转换领域结果为DTO响应对象
     */
    private QuarterlyBudgetRealTimeCalculationResponseDTO convertToRealTimeCalculationResponseDTO(RealTimeCalculationResult result) {
        QuarterlyBudgetRealTimeCalculationResponseDTO response = new QuarterlyBudgetRealTimeCalculationResponseDTO();

        // 基本金额信息
        response.setAnnualRevenueRemainingBudget(result.getAnnualRevenueRemainingBudget());
        response.setAnnualExpenditureRemainingBudget(result.getAnnualExpenditureRemainingBudget());
        response.setProjectExpenditureBudgetTotal(result.getProjectExpenditureBudgetTotal());
        response.setIndirectCostBudgetTotal(result.getIndirectCostBudgetTotal());
        response.setProjectMarginalProfit(result.getProjectMarginalProfit());
        response.setProjectMarginalProfitRate(result.getProjectMarginalProfitRate());
        response.setProjectNetProfit(result.getProjectNetProfit());
        response.setProjectNetProfitRate(result.getProjectNetProfitRate());

        // 转换预算科目明细直接成本列表
        if (result.getSubjectDirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO> subjectDirectCostList =
                result.getSubjectDirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.SubjectDirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setAnnualExpenditureBudgetAmount(info.getAnnualExpenditureBudgetAmount());
                        dto.setAnnualRemainExpendBudget(info.getAnnualRemainExpendBudget());
                        dto.setIndirectCostReferenceAmount(info.getIndirectCostReferenceAmount());
                        dto.setIndirectCostBudgetAmount(info.getIndirectCostBudgetAmount());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setSubjectDirectCostList(subjectDirectCostList);
        }

        // 转换本中心间接成本列表
        if (result.getCenterIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO> centerIndirectCostList =
                result.getCenterIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.CenterIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setCenterIndirectCostList(centerIndirectCostList);
        }

        // 转换非经营中心间接成本列表
        if (result.getNonOperatingIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO> nonOperatingIndirectCostList =
                result.getNonOperatingIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.NonOperatingIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setNonOperatingIndirectCostList(nonOperatingIndirectCostList);
        }

        // 转换综合管理间接成本列表
        if (result.getComprehensiveIndirectCostList() != null) {
            List<QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO> comprehensiveIndirectCostList =
                result.getComprehensiveIndirectCostList().stream()
                    .map(info -> {
                        QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO dto =
                            new QuarterlyBudgetRealTimeCalculationResponseDTO.ComprehensiveIndirectCostDTO();
                        dto.setBudgetSubjectCode(info.getBudgetSubjectCode());
                        dto.setBudgetSubjectName(info.getBudgetSubjectName());
                        dto.setSubjectDescription(info.getSubjectDescription());
                        dto.setExpenditureBudgetAmount(info.getExpenditureBudgetAmount());
                        return dto;
                    })
                    .collect(Collectors.toList());
            response.setComprehensiveIndirectCostList(comprehensiveIndirectCostList);
        }

        return response;
    }

    /**
     * 统计并记录子表数据信息
     *
     * @param entity 季度预算实体
     */
    private void logSubTableStatistics(CostQuarterlyBudgetEntity entity) {
        int totalDetailCount = 0;

        if (entity.getCostQuarterlyBudgetProcPkgDetailList() != null && !entity.getCostQuarterlyBudgetProcPkgDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetProcPkgDetailList().size();
            totalDetailCount += count;
            log.info("采办包明细数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetMaterialDetailList() != null && !entity.getCostQuarterlyBudgetMaterialDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetMaterialDetailList().size();
            totalDetailCount += count;
            log.info("原材料明细数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetSubjectDirectCostList() != null && !entity.getCostQuarterlyBudgetSubjectDirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetSubjectDirectCostList().size();
            totalDetailCount += count;
            log.info("预算科目直接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetCenterIndirectCostList() != null && !entity.getCostQuarterlyBudgetCenterIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetCenterIndirectCostList().size();
            totalDetailCount += count;
            log.info("本中心间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetCompMageIndirectCostList() != null && !entity.getCostQuarterlyBudgetCompMageIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetCompMageIndirectCostList().size();
            totalDetailCount += count;
            log.info("综合管理间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList() != null && !entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList().size();
            totalDetailCount += count;
            log.info("非经营中心间接成本数量: {}", count);
        }

        if (entity.getCostQuarterlyBudgetRevenueDetailList() != null && !entity.getCostQuarterlyBudgetRevenueDetailList().isEmpty()) {
            int count = entity.getCostQuarterlyBudgetRevenueDetailList().size();
            totalDetailCount += count;
            log.info("收入明细数量: {}", count);
        }

        log.info("季度预算子表明细总数量: {} (注意：当前版本只保存主表，子表保存功能待完善)", totalDetailCount);
    }
}
